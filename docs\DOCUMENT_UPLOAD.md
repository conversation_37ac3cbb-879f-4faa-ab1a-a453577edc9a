# Document Upload System

## Overview

The Document Upload System allows users to upload PDF documents that were previously generated by the Legal Document Issuance System (LDIS). The system validates the authenticity of uploaded documents by checking for embedded metadata and extracts the document data for storage in the database.

## Features

### 1. PDF Validation
- **System Authentication**: Only PDFs generated by LDIS are accepted
- **Metadata Validation**: Checks for proper metadata structure
- **File Type Validation**: Only PDF files are allowed
- **File Size Limit**: Maximum 10MB file size

### 2. Data Extraction
- **Embedded Metadata**: Extracts JSON metadata from PDF text
- **Document Information**: Retrieves document name, applicant name, and form data
- **Photo Support**: Handles documents with embedded applicant photos

### 3. Database Storage
- **Document Table**: Stores extracted data in the documents table
- **Status Tracking**: Sets initial status as "uploaded"
- **User Association**: Links documents to the uploading user

## How It Works

### 1. Upload Process
1. User selects a PDF file through the upload interface
2. System validates file type and size
3. PDF content is extracted using pdf-parse library
4. Metadata is searched for in the PDF text content

### 2. Validation Process
1. **Metadata Extraction**: Looks for `LDIS_METADATA:` marker in PDF text
2. **JSON Parsing**: Parses the embedded JSON metadata
3. **Structure Validation**: Validates required fields and data types
4. **System Check**: Verifies `generation_info.system` equals "Legal Document Issuance System"

### 3. Storage Process
1. **Data Conversion**: Converts document data to JSON string
2. **Database Insert**: Creates new record in documents table
3. **File Cleanup**: Removes temporary PDF file
4. **Success Response**: Returns document information to user

## API Endpoints

### POST /api/documents/upload

Uploads and processes a PDF document.

**Request:**
- `pdfFile`: PDF file (multipart/form-data)
- `userId`: User ID (string)

**Response (Success):**
```json
{
  "message": "Document uploaded and processed successfully",
  "documentId": 123,
  "documentName": "MEDICAL CERTIFICATE.pdf",
  "applicantName": "John Doe",
  "fieldCount": 13,
  "hasPhoto": true
}
```

**Response (Error):**
```json
{
  "error": "This PDF does not contain valid LDIS metadata...",
  "warning": "PDF validation failed"
}
```

## User Interface

### Upload Page (`/upload`)

The upload page provides:
- **File Selection**: Drag-and-drop or click to select PDF files
- **Progress Indication**: Shows upload and processing status
- **Validation Feedback**: Clear error messages for invalid files
- **Success Summary**: Displays extracted document information

### Navigation

The upload functionality is accessible through:
- **Sidebar Menu**: "Upload document" link in the navigation section
- **Breadcrumb**: Shows "Home > Upload Document" path

## Error Handling

### Common Error Scenarios

1. **Invalid File Type**
   - Error: "Only PDF files are allowed"
   - Solution: Select a PDF file

2. **File Too Large**
   - Error: "File size must be less than 10MB"
   - Solution: Compress or select smaller PDF

3. **No LDIS Metadata**
   - Error: "This PDF does not contain valid LDIS metadata..."
   - Solution: Only upload PDFs generated by LDIS

4. **Corrupted PDF**
   - Error: "Failed to read PDF content. The file may be corrupted..."
   - Solution: Re-download or regenerate the PDF

5. **Wrong System**
   - Error: "This document was not generated by the Legal Document Issuance System"
   - Solution: Only upload LDIS-generated documents

## Technical Implementation

### Dependencies
- **pdf-parse**: Extracts text content from PDF files
- **@types/pdf-parse**: TypeScript definitions for pdf-parse

### Key Files
- `src/app/upload/page.tsx`: Upload page UI component
- `src/app/api/documents/upload/route.ts`: Upload API endpoint
- `src/lib/pdf-metadata-utils.ts`: PDF metadata utilities
- `src/lib/database.ts`: Database operations

### Database Schema

The uploaded documents are stored in the `documents` table:

```sql
CREATE TABLE documents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_name TEXT NOT NULL,
  applicant_name TEXT NOT NULL,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  document_data BLOB,
  status TEXT DEFAULT 'pending',
  approved_at DATETIME,
  user_id INTEGER NOT NULL,
  is_archive BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Security Considerations

1. **File Type Validation**: Only PDF files are accepted
2. **Size Limits**: 10MB maximum file size prevents abuse
3. **Metadata Validation**: Ensures only LDIS documents are processed
4. **Temporary File Cleanup**: Removes uploaded files after processing
5. **User Authentication**: Documents are linked to authenticated users

## Testing

Run the test script to verify functionality:

```bash
node scripts/test-pdf-upload.js
```

This tests:
- Metadata extraction from sample text
- Metadata validation
- System validation
- Invalid metadata rejection
- Malformed metadata handling

## Future Enhancements

1. **Batch Upload**: Support multiple file uploads
2. **Preview Mode**: Show document preview before processing
3. **Duplicate Detection**: Check for already uploaded documents
4. **Advanced Validation**: Verify document signatures or checksums
5. **Audit Trail**: Track upload history and changes
