#!/usr/bin/env node

/**
 * Test script to verify PDF generation with metadata embedding
 * This script creates a simple PDF with LDIS metadata to test the upload functionality
 */

const { jsPDF } = require("jspdf");
const fs = require("fs");
const path = require("path");

async function createTestPDF() {
  try {
    console.log("🧪 Creating test PDF with LDIS metadata...\n");

    // Create a new PDF document
    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "A4",
    });

    // Add some content to the PDF
    pdf.setFontSize(16);
    pdf.text("LDIS Test Document", 20, 30);

    pdf.setFontSize(12);
    pdf.text(
      "This is a test document generated for testing the upload functionality.",
      20,
      50
    );
    pdf.text("Document Type: Medical Certificate", 20, 70);
    pdf.text("Applicant: Test User", 20, 90);
    pdf.text("Generated: " + new Date().toISOString(), 20, 110);

    // Create test metadata
    const testMetadata = {
      document_name: "TEST_MEDICAL_CERTIFICATE.pdf",
      applicant_name: "Test, User, A., Jr.",
      document_data: {
        LAST_NAME: "Test",
        FIRST_NAME: "User",
        MIDDLE_INITIAL: "A.",
        SUFFIX: "Jr.",
        AGE: "25",
        BARANGAY: "Test Barangay",
        MUNICIPALITY: "Test Municipality",
        PROVINCE: "Test Province",
        PARAGRAPH: "Test paragraph content",
        DAY: "31",
        MONTH: "July",
        YEAR: "2025",
        OR_NUMBER: "TEST123456",
      },
      generation_info: {
        system: "Legal Document Issuance System",
      },
    };

    const metadataJson = JSON.stringify(testMetadata);

    // Method 1: Add as invisible text (original method)
    pdf.setFontSize(1);
    pdf.setTextColor(255, 255, 255); // White text
    pdf.text(`LDIS_METADATA:${metadataJson}`, 1, 296); // Near bottom of A4

    // Method 2: Add as a separate metadata page
    pdf.addPage();
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0); // Black text
    pdf.text("LDIS Document Metadata", 20, 30);
    pdf.text(
      "This page contains embedded metadata for document verification.",
      20,
      50
    );

    // Split the metadata JSON across multiple lines for better readability
    pdf.setFontSize(8);
    pdf.text("LDIS_METADATA:", 20, 80);

    // Add the JSON in chunks to fit on the page
    const jsonLines = JSON.stringify(testMetadata, null, 2).split("\n");
    let yPosition = 90;

    for (const line of jsonLines) {
      if (yPosition > 280) {
        // If we're near the bottom of the page
        pdf.addPage();
        yPosition = 20;
      }
      pdf.text(line, 20, yPosition);
      yPosition += 4;
    }

    // Also add the compact version for parsing
    pdf.addPage();
    pdf.setFontSize(10);
    pdf.text("Compact Metadata for Parsing:", 20, 30);
    pdf.text(`LDIS_METADATA:${metadataJson}`, 20, 50);

    // Save the PDF
    const outputDir = path.join(process.cwd(), "temp");
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const outputPath = path.join(outputDir, "test-ldis-document.pdf");
    const pdfBuffer = Buffer.from(pdf.output("arraybuffer"));
    fs.writeFileSync(outputPath, pdfBuffer);

    console.log("✅ Test PDF created successfully!");
    console.log(`📄 File saved to: ${outputPath}`);
    console.log(`📊 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    console.log(`🔍 Metadata embedded: ${metadataJson.length} characters`);

    console.log("\n📋 Test Metadata Summary:");
    console.log(`   Document: ${testMetadata.document_name}`);
    console.log(`   Applicant: ${testMetadata.applicant_name}`);
    console.log(`   System: ${testMetadata.generation_info.system}`);
    console.log(`   Fields: ${Object.keys(testMetadata.document_data).length}`);

    console.log("\n🧪 You can now test the upload functionality by:");
    console.log("1. Navigate to http://localhost:3000/upload");
    console.log("2. Upload the generated PDF file");
    console.log("3. Verify that the metadata is extracted correctly");

    return outputPath;
  } catch (error) {
    console.error("❌ Error creating test PDF:", error);
    throw error;
  }
}

// Run the test
createTestPDF()
  .then((filePath) => {
    console.log(`\n🎉 Test PDF generation completed successfully!`);
    console.log(`File: ${filePath}`);
  })
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
