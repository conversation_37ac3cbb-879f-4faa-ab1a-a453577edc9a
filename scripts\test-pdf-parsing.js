#!/usr/bin/env node

/**
 * Test script to verify PDF parsing functionality
 * This script tests if pdf-parse can extract text from our generated PDFs
 */

const fs = require('fs');
const path = require('path');

async function testPdfParsing() {
  try {
    console.log("🧪 Testing PDF Parsing...\n");

    // Check if test PDF exists
    const testPdfPath = path.join(process.cwd(), 'temp', 'test-ldis-document.pdf');
    
    if (!fs.existsSync(testPdfPath)) {
      console.log("❌ Test PDF not found. Running PDF generation first...");
      
      // Run the PDF generation script
      const { execSync } = require('child_process');
      execSync('node scripts/test-pdf-generation.js', { stdio: 'inherit' });
      
      if (!fs.existsSync(testPdfPath)) {
        throw new Error('Failed to generate test PDF');
      }
    }

    console.log("📄 Test PDF found:", testPdfPath);
    console.log("📊 File size:", (fs.statSync(testPdfPath).size / 1024).toFixed(2), "KB");

    // Test PDF parsing
    console.log("\n🔍 Testing PDF text extraction...");

    const pdfParse = require('pdf-parse');
    const pdfBuffer = fs.readFileSync(testPdfPath);
    
    console.log("📋 PDF buffer size:", pdfBuffer.length, "bytes");

    const pdfData = await pdfParse(pdfBuffer);
    
    console.log("\n📄 PDF Parsing Results:");
    console.log("Pages:", pdfData.numpages);
    console.log("Text length:", pdfData.text.length);
    console.log("Info:", JSON.stringify(pdfData.info, null, 2));

    console.log("\n📝 Extracted Text Preview (first 500 chars):");
    console.log("=" + "=".repeat(50));
    console.log(pdfData.text.substring(0, 500));
    console.log("=" + "=".repeat(50));

    // Check for LDIS metadata
    console.log("\n🔍 Searching for LDIS metadata...");
    const hasLDISMarker = pdfData.text.includes('LDIS_METADATA:');
    console.log("Contains LDIS_METADATA marker:", hasLDISMarker);

    if (hasLDISMarker) {
      console.log("✅ LDIS metadata marker found!");
      
      // Try to extract the metadata
      const metadataMatch = pdfData.text.match(/LDIS_METADATA:({.*})/);
      if (metadataMatch && metadataMatch[1]) {
        console.log("✅ Metadata JSON found!");
        try {
          const metadata = JSON.parse(metadataMatch[1]);
          console.log("✅ Metadata parsed successfully!");
          console.log("📋 Metadata Summary:");
          console.log("   Document:", metadata.document_name);
          console.log("   Applicant:", metadata.applicant_name);
          console.log("   System:", metadata.generation_info.system);
          console.log("   Fields:", Object.keys(metadata.document_data).length);
        } catch (parseError) {
          console.log("❌ Failed to parse metadata JSON:", parseError.message);
          console.log("Raw metadata:", metadataMatch[1].substring(0, 200) + "...");
        }
      } else {
        console.log("❌ LDIS metadata marker found but JSON not extractable");
        
        // Show context around the marker
        const markerIndex = pdfData.text.indexOf('LDIS_METADATA:');
        const contextStart = Math.max(0, markerIndex - 50);
        const contextEnd = Math.min(pdfData.text.length, markerIndex + 200);
        console.log("Context around marker:");
        console.log(pdfData.text.substring(contextStart, contextEnd));
      }
    } else {
      console.log("❌ LDIS metadata marker not found in extracted text");
      
      // Show the full text for debugging
      console.log("\n📝 Full Extracted Text:");
      console.log("=" + "=".repeat(50));
      console.log(pdfData.text);
      console.log("=" + "=".repeat(50));
    }

    // Test with the metadata utils
    console.log("\n🧪 Testing with metadata utils...");
    
    require("ts-node/register");
    const { extractMetadataFromPdfText } = require("../src/lib/pdf-metadata-utils.ts");
    
    const extractedMetadata = extractMetadataFromPdfText(pdfData.text);
    
    if (extractedMetadata) {
      console.log("✅ Metadata extraction with utils successful!");
      console.log("📋 Extracted metadata:");
      console.log(JSON.stringify(extractedMetadata, null, 2));
    } else {
      console.log("❌ Metadata extraction with utils failed");
    }

  } catch (error) {
    console.error("❌ Test failed with error:", error);
    process.exit(1);
  }
}

// Run the test
testPdfParsing();
