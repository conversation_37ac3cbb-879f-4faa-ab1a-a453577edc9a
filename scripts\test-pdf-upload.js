#!/usr/bin/env node

/**
 * Test script for PDF upload functionality
 * This script tests the PDF metadata extraction and validation
 */

require("ts-node/register");

async function testPdfUpload() {
  try {
    console.log("🧪 Testing PDF Upload Functionality...\n");

    // Import the required modules
    const {
      extractMetadataFromPdfText,
      validateDocumentMetadata,
      EXAMPLE_METADATA,
    } = require("../src/lib/pdf-metadata-utils.ts");

    console.log("1. Testing metadata extraction from sample text...");
    
    // Create a sample PDF text with embedded metadata
    const samplePdfText = `
      Some PDF content here...
      
      LDIS_METADATA:${JSON.stringify(EXAMPLE_METADATA)}
      
      More content...
    `;

    const extractedMetadata = extractMetadataFromPdfText(samplePdfText);
    
    if (extractedMetadata) {
      console.log("✅ Metadata extraction successful!");
      console.log("   Document:", extractedMetadata.document_name);
      console.log("   Applicant:", extractedMetadata.applicant_name);
      console.log("   System:", extractedMetadata.generation_info.system);
      console.log("   Fields:", Object.keys(extractedMetadata.document_data).length);
    } else {
      console.log("❌ Metadata extraction failed!");
      return;
    }

    console.log("\n2. Testing metadata validation...");
    
    const isValid = validateDocumentMetadata(extractedMetadata);
    if (isValid) {
      console.log("✅ Metadata validation successful!");
    } else {
      console.log("❌ Metadata validation failed!");
      return;
    }

    console.log("\n3. Testing system validation...");
    
    if (extractedMetadata.generation_info.system === "Legal Document Issuance System") {
      console.log("✅ System validation successful!");
    } else {
      console.log("❌ System validation failed!");
      console.log("   Expected: Legal Document Issuance System");
      console.log("   Found:", extractedMetadata.generation_info.system);
      return;
    }

    console.log("\n4. Testing invalid metadata...");
    
    const invalidText = "Some PDF content without LDIS metadata";
    const invalidMetadata = extractMetadataFromPdfText(invalidText);
    
    if (invalidMetadata === null) {
      console.log("✅ Invalid metadata correctly rejected!");
    } else {
      console.log("❌ Invalid metadata incorrectly accepted!");
      return;
    }

    console.log("\n5. Testing malformed metadata...");
    
    const malformedText = "LDIS_METADATA:{invalid json}";
    const malformedMetadata = extractMetadataFromPdfText(malformedText);
    
    if (malformedMetadata === null) {
      console.log("✅ Malformed metadata correctly rejected!");
    } else {
      console.log("❌ Malformed metadata incorrectly accepted!");
      return;
    }

    console.log("\n🎉 All PDF upload tests passed!");
    console.log("\nThe upload functionality should work correctly with:");
    console.log("- PDF files generated by LDIS");
    console.log("- Proper metadata validation");
    console.log("- System authentication");
    console.log("- Error handling for invalid files");

  } catch (error) {
    console.error("❌ Test failed with error:", error);
    process.exit(1);
  }
}

// Run the test
testPdfUpload();
