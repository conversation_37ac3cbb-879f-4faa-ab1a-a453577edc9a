import { NextRequest, NextResponse } from 'next/server';
import { createDocument } from '@/lib/database';
import { extractMetadataFromPdfText, validateDocumentMetadata } from '@/lib/pdf-metadata-utils';
import { unlink, mkdir } from 'fs/promises';
import { join } from 'path';
import { writeFile } from 'fs/promises';
import { existsSync } from 'fs';

// Dynamic import for pdf-parse to avoid build issues
async function parsePdf(buffer: Buffer) {
  const pdfParse = await import('pdf-parse');
  return pdfParse.default(buffer);
}

/**
 * POST /api/documents/upload - Upload and process PDF documents
 */
export async function POST(request: NextRequest) {
  let tempFilePath: string | null = null;
  
  try {
    const formData = await request.formData();
    
    // Get form fields
    const pdfFile = formData.get('pdfFile') as File;
    const userId = formData.get('userId') as string;
    
    // Validate required fields
    if (!pdfFile || !userId) {
      return NextResponse.json(
        { error: 'PDF file and user ID are required' },
        { status: 400 }
      );
    }
    
    // Validate file type
    if (!pdfFile.name.toLowerCase().endsWith('.pdf')) {
      return NextResponse.json(
        { error: 'Only PDF files are allowed' },
        { status: 400 }
      );
    }
    
    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (pdfFile.size > maxSize) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }
    
    // Create temporary file for PDF processing
    const tempDir = join(process.cwd(), 'temp');
    const timestamp = Date.now();
    const tempFileName = `upload_${timestamp}_${pdfFile.name}`;
    tempFilePath = join(tempDir, tempFileName);

    // Ensure temp directory exists
    try {
      if (!existsSync(tempDir)) {
        await mkdir(tempDir, { recursive: true });
      }
      await writeFile(tempFilePath, Buffer.from(await pdfFile.arrayBuffer()));
    } catch (error) {
      console.error('Error writing temp file:', error);
      return NextResponse.json(
        { error: 'Failed to process PDF file' },
        { status: 500 }
      );
    }
    
    // Extract text from PDF
    let pdfText: string;
    try {
      const pdfBuffer = Buffer.from(await pdfFile.arrayBuffer());
      const pdfData = await parsePdf(pdfBuffer);
      pdfText = pdfData.text;

    } catch (error) {
      console.error('Error parsing PDF:', error);
      return NextResponse.json(
        { error: 'Failed to read PDF content. The file may be corrupted or password-protected.' },
        { status: 400 }
      );
    }
    
    // Extract metadata from PDF text
    const metadata = extractMetadataFromPdfText(pdfText);

    if (!metadata) {
      return NextResponse.json(
        {
          error: 'This PDF does not contain valid LDIS metadata. Only documents generated by the Legal Document Issuance System can be uploaded. Please ensure you are uploading a PDF that was downloaded from this system.',
          warning: 'PDF validation failed'
        },
        { status: 400 }
      );
    }
    
    // Validate metadata structure
    if (!validateDocumentMetadata(metadata)) {
      return NextResponse.json(
        { 
          error: 'Invalid document metadata structure. The PDF may be corrupted or from an incompatible version.',
          warning: 'Metadata validation failed'
        },
        { status: 400 }
      );
    }
    
    // Check if the document is from LDIS
    if (metadata.generation_info.system !== "Legal Document Issuance System") {
      return NextResponse.json(
        { 
          error: `This document was not generated by the Legal Document Issuance System. Found system: "${metadata.generation_info.system}"`,
          warning: 'System validation failed'
        },
        { status: 400 }
      );
    }
    
    // Convert document data to JSON string for storage
    const documentDataJson = JSON.stringify(metadata.document_data);
    const documentDataBuffer = Buffer.from(documentDataJson, 'utf8');
    
    // Insert document into database
    try {
      const documentId = await createDocument(
        metadata.document_name,
        metadata.applicant_name,
        documentDataBuffer,
        'uploaded', // status
        parseInt(userId),
        false // is_archive
      );
      
      console.log('Document uploaded successfully with ID:', documentId);
      
      return NextResponse.json(
        {
          message: 'Document uploaded and processed successfully',
          documentId,
          documentName: metadata.document_name,
          applicantName: metadata.applicant_name,
          fieldCount: Object.keys(metadata.document_data).length,
          hasPhoto: 'applicants_photo' in metadata.document_data
        },
        { status: 201 }
      );
      
    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: 'Failed to save document to database' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred while processing the upload' },
      { status: 500 }
    );
  } finally {
    // Clean up temporary file
    if (tempFilePath) {
      try {
        await unlink(tempFilePath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temporary file:', cleanupError);
      }
    }
  }
}
