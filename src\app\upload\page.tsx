"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Upload,
  FileText,
  AlertCircle,
  CheckCircle,
  Loader2,
  X,
  Info,
} from "lucide-react";
import { toast } from "sonner";

interface UploadState {
  stage: "initial" | "uploading" | "success" | "error";
  file: File | null;
  error: string;
  success: string;
  documentInfo: {
    documentId?: number;
    documentName?: string;
    applicantName?: string;
    fieldCount?: number;
    hasPhoto?: boolean;
  } | null;
}

export default function UploadPage() {
  const [state, setState] = useState<UploadState>({
    stage: "initial",
    file: null,
    error: "",
    success: "",
    documentInfo: null,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setState((prev) => ({
        ...prev,
        file,
        error: "",
        success: "",
        documentInfo: null,
      }));
    }
  };

  const clearFile = () => {
    setState((prev) => ({
      ...prev,
      file: null,
      error: "",
      success: "",
      documentInfo: null,
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUpload = async () => {
    if (!state.file) {
      toast.error("Please select a PDF file to upload");
      return;
    }

    setState((prev) => ({
      ...prev,
      stage: "uploading",
      error: "",
      success: "",
    }));

    try {
      const formData = new FormData();
      formData.append("pdfFile", state.file);
      formData.append("userId", "1"); // TODO: Get from auth context

      const response = await fetch("/api/documents/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        setState((prev) => ({
          ...prev,
          stage: "error",
          error: result.error || "Upload failed",
        }));

        if (result.warning) {
          toast.error(result.error, {
            description: result.warning,
          });
        } else {
          toast.error(result.error);
        }
        return;
      }

      setState((prev) => ({
        ...prev,
        stage: "success",
        success: result.message,
        documentInfo: {
          documentId: result.documentId,
          documentName: result.documentName,
          applicantName: result.applicantName,
          fieldCount: result.fieldCount,
          hasPhoto: result.hasPhoto,
        },
      }));

      toast.success("Document uploaded successfully!", {
        description: `${result.documentName} has been processed and stored.`,
      });
    } catch (error) {
      console.error("Upload error:", error);
      setState((prev) => ({
        ...prev,
        stage: "error",
        error: "Network error occurred while uploading",
      }));
      toast.error("Upload failed", {
        description: "Please check your connection and try again.",
      });
    }
  };

  const resetUpload = () => {
    setState({
      stage: "initial",
      file: null,
      error: "",
      success: "",
      documentInfo: null,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Upload Document</h1>
          <p className="text-muted-foreground">
            Upload PDF documents generated by the Legal Document Issuance System
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Document Upload
            </CardTitle>
            <CardDescription>
              Upload a PDF document to extract and store its embedded data. Only
              documents generated by LDIS are accepted.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Information Alert */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> Only PDF documents generated by the
                Legal Document Issuance System can be uploaded. The system will
                validate the document&apos;s authenticity before processing.
              </AlertDescription>
            </Alert>

            {/* File Upload Section */}
            <div className="space-y-4">
              <Label htmlFor="pdfFile" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                PDF Document *
              </Label>

              <div className="space-y-2">
                <Input
                  id="pdfFile"
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  disabled={state.stage === "uploading"}
                  className="cursor-pointer"
                />

                {state.file && (
                  <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {state.file.name}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        ({(state.file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFile}
                      disabled={state.stage === "uploading"}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Error Alert */}
            {state.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{state.error}</AlertDescription>
              </Alert>
            )}

            {/* Success Alert */}
            {state.success && state.documentInfo && (
              <Alert className="border-green-200 bg-green-50 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">{state.success}</p>
                    <div className="text-sm space-y-1">
                      <p>
                        <strong>Document:</strong>{" "}
                        {state.documentInfo.documentName}
                      </p>
                      <p>
                        <strong>Applicant:</strong>{" "}
                        {state.documentInfo.applicantName}
                      </p>
                      <p>
                        <strong>Fields extracted:</strong>{" "}
                        {state.documentInfo.fieldCount}
                      </p>
                      {state.documentInfo.hasPhoto && (
                        <p>
                          <strong>Contains photo:</strong> Yes
                        </p>
                      )}
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              {state.stage === "success" ? (
                <Button onClick={resetUpload} className="flex-1">
                  Upload Another Document
                </Button>
              ) : (
                <Button
                  onClick={handleUpload}
                  disabled={!state.file || state.stage === "uploading"}
                  className="flex-1"
                >
                  {state.stage === "uploading" ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload & Process
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
